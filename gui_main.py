# -*- coding: utf-8 -*-
"""
股票筛选器主程序
基于指南针软件的股票多空资金数据筛选工具
GUI主入口模块
"""

# 抑制PyTorch警告（在所有其他导入之前）
import os
import warnings
os.environ['PYTORCH_DISABLE_PIN_MEMORY_WARNING'] = '1'
warnings.filterwarnings("ignore", message=".*pin_memory.*")
warnings.filterwarnings("ignore", category=UserWarning, module="torch.*")

import tkinter as tk
import threading
import logging
import sys
from typing import List, Dict, Any, Optional
import queue
import traceback

from config import GUI_CONFIG, APP_CONFIG, COMPASS_SOFTWARE, update_ocr_region_config
from data_processor import DataProcessor
from compass_automator import CompassAutomator
from region_selector import RegionSelector
from ocr_manager_optimized import get_global_ocr_manager

# 导入mixin模块
from gui_setup import GUISetupMixin
from gui_file_operations import GUIFileOperationsMixin  
from gui_ocr_operations import GUIOCROperationsMixin
from gui_analysis import GUIAnalysisMixin
from gui_display import GUIDisplayMixin
from gui_handlers import GUIHandlersMixin


class StockScreenerGUI(
    GUISetupMixin,
    GUIFileOperationsMixin, 
    GUIOCROperationsMixin,
    GUIAnalysisMixin,
    GUIDisplayMixin,
    GUIHandlersMixin
):
    """股票筛选器GUI主类"""
    
    def __init__(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.setup_logging()
        self.setup_window()
        self.setup_variables()
        self.setup_widgets()
        self.setup_layout()
        
        # 初始化组件
        self.data_processor = DataProcessor()
        self.compass_automator = None
        self.ocr_manager = get_global_ocr_manager()
        self.is_processing = False
        self.current_excel_path = ""
        self.selected_region = None  # 存储选择的区域坐标
        
        # 消息队列用于线程间通信
        self.message_queue = queue.Queue()
        self.check_message_queue()
        
        # 日志处理器
        self.log_handler = None  # 将在setup_widgets中初始化
        
        # 读取配置文件中的区域信息并初始化OCR
        self.load_saved_region()
        self.init_ocr_early()
        
        # 更新界面状态（在所有组件都初始化完成后）
        self.update_ui_after_init()
    
    def run(self):
        """运行GUI"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.logger.info("用户中断程序")
        except Exception as e:
            self.logger.error(f"GUI运行错误: {str(e)}")
            tk.messagebox.showerror("错误", f"程序运行错误: {str(e)}")
        finally:
            if self.compass_automator:
                self.compass_automator.close_compass_software()


def main():
    """主函数"""
    try:
        app = StockScreenerGUI()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        print(traceback.format_exc())


if __name__ == "__main__":
    main()